<template>
  <div class="user-management">
    <!-- 统计区域 -->
    <div class="summary-section">
      <!-- 摘要卡片区 -->
      <div class="summary-cards">
        <div class="summary-card">
          <div class="summary-content">
            <span class="summary-title">{{ $T("用户（个）") }}</span>
            <span class="summary-value user-value">
              {{ statCards.user || "--" }}
            </span>
          </div>
        </div>
        <div class="summary-card">
          <div class="summary-content">
            <span class="summary-title">{{ $T("资源（个）") }}</span>
            <span class="summary-value resource-value">
              {{ statCards.resource || "--" }}
            </span>
          </div>
        </div>
        <div class="summary-card">
          <div class="summary-content">
            <span class="summary-title">{{ $T("站点（个）") }}</span>
            <span class="summary-value site-value">
              {{ statCards.site || "--" }}
            </span>
          </div>
        </div>
        <div class="summary-card">
          <div class="summary-content">
            <span class="summary-title">{{ $T("设备（个）") }}</span>
            <span class="summary-value device-value">
              {{ statCards.device || "--" }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据操作区域 -->
    <div class="data-section">
      <!-- 筛选栏 -->
      <div class="filter-bar vpp-filter-bar">
        <el-input
          class="vpp-search-input"
          :placeholder="$T('请输入关键字')"
          v-model="search.keyword"
          clearable
          prefix-icon="el-icon-search"
          size="small"
          @change="onSearchKeywordChange"
        />
        <div class="vpp-select-group">
          <div class="cascader-box">
            <span class="cascader-text">{{ $T("区域") }}</span>
            <el-cascader
              v-model="search.area"
              :options="regionOptions"
              :placeholder="$T('请选择省/市/区')"
              size="small"
              clearable
              change-on-select
              :props="{
                checkStrictly: true,
                value: 'code',
                label: 'name',
                children: 'children',
                emitPath: true,
                expandTrigger: 'click'
              }"
              class="cascader-select"
              @change="onSearchAreaChange"
            />
          </div>
        </div>
        <div class="vpp-action-buttons">
          <el-button
            type="danger"
            size="small"
            :disabled="selectedUsers.length === 0"
            @click="onBatchDelete"
            plain
          >
            {{ $T("批量删除") }}
          </el-button>
          <el-button type="primary" size="small" @click="onAddUser">
            {{ $T("新增代理用户") }}
          </el-button>
        </div>
      </div>

      <!-- 表格区域 -->
      <el-table
        class="flex1"
        :data="tableData"
        height="true"
        ref="userTable"
        style="width: 100%"
        @selection-change="onSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column :label="$T('序号')" width="80">
          <template slot-scope="scope">
            {{ (pagination.page - 1) * pagination.size + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          :label="$T('用户名称')"
          min-width="200"
          :formatter="textFormatter"
        />
        <el-table-column prop="area" :label="$T('区域')" width="200" />
        <el-table-column
          prop="count"
          :label="$T('资源数量（个）')"
          width="160"
          :formatter="textFormatter"
        />
        <el-table-column :label="$T('操作')" width="200" fixed="right">
          <template slot-scope="scope">
            <span
              class="action-link detail-link"
              @click.stop="onDetail(scope.row)"
            >
              {{ $T("详情") }}
            </span>
            <span class="action-link edit-link" @click.stop="onEdit(scope.row)">
              {{ $T("编辑") }}
            </span>
            <span
              class="action-link delete-link"
              @click.stop="onDelete(scope.row)"
            >
              {{ $T("删除") }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页区域 -->
      <div class="table-footer">
        <el-pagination
          :current-page.sync="pagination.page"
          :page-size.sync="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total,sizes, prev, pager, next, jumper"
          @current-change="onPageChange"
          @size-change="onPageSizeChange"
        />
      </div>
    </div>

    <!-- 用户弹窗（新增/编辑） -->
    <AddUserDialog
      :visible="userDialog.visible"
      :mode="userDialog.mode"
      :userData="userDialog.userData"
      :regionOptions="regionOptions"
      @close="onUserDialogClose"
      @save="onUserDialogSave"
      @update="onUserDialogUpdate"
    />

    <!-- 用户详情弹窗 -->
    <UserDetailDrawer
      :visibleTrigger_in="userDetailDialog.visibleTrigger"
      :closeTrigger_in="userDetailDialog.closeTrigger"
      :inputData_in="userDetailDialog.userData"
    />
  </div>
</template>

<script>
import AddUserDialog from "./AddUserDialog.vue";
import UserDetailDrawer from "./UserDetailDrawer.vue";
import common from "eem-base/utils/common";

import {
  createUser,
  getUserPage,
  updateUser,
  deleteUser
} from "@/api/user-management";
import { getGeographicalData } from "@/api/base-config";

export default {
  name: "VppManagement",
  components: {
    AddUserDialog,
    UserDetailDrawer
  },
  props: {
    vppId: {
      type: Number,
      default: null
    },
    userId: {
      type: Number,
      default: null
    },
    statData: {
      type: Object,
      default: () => ({ user: 0, resource: 0, site: 0, device: 0 })
    },
    province: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      selectedUsers: [], // 选中的用户列表
      statCards: {
        user: 0,
        resource: 0,
        site: 0,
        device: 0
      },
      search: { keyword: "", area: [] },
      regionOptions: [],
      geographicalData: null, // 存储地理数据
      // 级联选择器配置：允许选择省/市/区任意级别
      cascaderProps: {
        checkStrictly: true, // 允许选择任意级别
        value: "code",
        label: "name",
        children: "children",
        emitPath: true, // 返回完整路径
        expandTrigger: "click" // 点击展开
      },
      tableData: [],
      pagination: { total: 0, page: 1, size: 10 },
      userDialog: {
        visible: false,
        mode: "add", // "add" 或 "edit"
        userData: {}
      },
      userDetailDialog: {
        visibleTrigger: 0,
        closeTrigger: 0,
        userData: {}
      },
      searchTimer: null // 搜索防抖定时器
    };
  },
  computed: {
    // 获取当前VPP ID
    currentVppId() {
      return this.vppId;
    },
    // 文本格式化器
    textFormatter() {
      return common.formatTextCol();
    }
  },

  watch: {
    vppId: {
      handler(newVppId) {
        if (newVppId) {
          this.loadData();
        } else {
          this.tableData = [];
          this.statCards = { user: 0, resource: 0, site: 0, device: 0 };
        }
      },
      immediate: true
    },
    statData: {
      handler(newStatData) {
        if (newStatData) {
          this.statCards = { ...newStatData };
        }
      },
      immediate: true,
      deep: true
    }
  },
  async mounted() {
    // 先加载地理数据，确保区域名称转换正常工作
    await this.loadGeographicalData();
    // watch 的 immediate: true 已经会触发数据加载，这里不需要重复调用
  },
  methods: {
    // 统一的数据加载方法
    async loadData() {
      if (this.currentVppId) {
        // 先加载地理数据，确保区域名称转换正常工作
        if (!this.geographicalData) {
          await this.loadGeographicalData();
        }

        // 然后加载用户数据和统计数据
        this.loadUsers();
        this.loadStatistics();
      } else {
        this.tableData = [];
        this.statCards = { user: 0, resource: 0, site: 0, device: 0 };
      }
    },

    // 加载地理数据
    async loadGeographicalData() {
      const response = await getGeographicalData();
      if (response.code === 0) {
        // 存储完整的地理数据用于区域名称转换
        this.geographicalData = response.data;

        // 转换为级联选择器格式
        this.regionOptions = this.transformGeographicalData(
          response.data,
          this.province
        );
      }
    },

    // 转换地理数据为级联选择器格式（支持省份、城市和区县三级）
    transformGeographicalData(data, provinceId) {
      if (!data || !Array.isArray(data)) return [];

      // 处理数据，支持省份、城市和区县三级
      const processedData = data.map(province => {
        const processedProvince = {
          code: province.code,
          name: province.name,
          children: []
        };

        // 如果有城市数据，保留城市级别
        if (province.children && Array.isArray(province.children)) {
          processedProvince.children = province.children.map(city => {
            const processedCity = {
              code: city.code,
              name: city.name,
              children: []
            };

            // 如果有区县数据，保留区县级别
            if (city.children && Array.isArray(city.children)) {
              processedCity.children = city.children.map(district => ({
                code: district.code,
                name: district.name
              }));
            }

            return processedCity;
          });
        }

        return processedProvince;
      });

      if (provinceId) {
        return processedData.filter(p => p.code === provinceId);
      }
      return processedData;
    },

    // 根据province、city和district ID/code获取区域名称
    getRegionName(provinceId, cityId, districtId) {
      if (!this.geographicalData || !Array.isArray(this.geographicalData)) {
        return "--";
      }

      let regionName = "";

      // 查找省份名称
      if (provinceId) {
        const province = this.geographicalData.find(p => p.code === provinceId);

        if (province) {
          regionName = province.name;

          // 查找城市名称
          if (cityId && province.children && Array.isArray(province.children)) {
            const city = province.children.find(c => c.code === cityId);

            if (city && city.name !== province.name) {
              regionName += "/" + city.name;
              // 查找区县名称
              // 查找区县名称
              if (districtId && city.children && Array.isArray(city.children)) {
                const district = city.children.find(d => d.code === districtId);
                if (district && district.name !== city.name) {
                  regionName += "/" + district.name;
                }
              }
            }
          }
        }
      }

      return regionName || "--";
    },

    // 防抖搜索
    debounceSearch() {
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.pagination.page = 1;
        this.loadUsers();
      }, 500);
    },

    // 搜索关键字变化处理
    onSearchKeywordChange() {
      this.pagination.page = 1;
      this.debounceSearch();
    },

    // 搜索区域变化处理
    onSearchAreaChange() {
      this.pagination.page = 1;
      this.loadUsers();
    },

    // 数据转换：将API返回的用户数据转换为表格显示格式
    transformUserData(apiUser) {
      // 构建区域代码数组用于级联选择器
      const areaArray = [];
      if (apiUser.province) {
        areaArray.push(apiUser.province);
        if (apiUser.city) {
          areaArray.push(apiUser.city);
          if (apiUser.district) {
            areaArray.push(apiUser.district);
          }
        }
      }

      return {
        id: apiUser.id,
        name: apiUser.userName,
        area: this.getRegionName(
          apiUser.province,
          apiUser.city,
          apiUser.district
        ), // 用于表格显示的字符串格式
        areaArray: areaArray, // 用于级联选择器的数组格式
        count: apiUser.resourceCount || 0,
        // 为了兼容 UserDetailDrawer 组件，同时提供两种字段名
        contact: apiUser.contactPerson,
        contactPerson: apiUser.contactPerson,
        phone: apiUser.phoneNumber,
        phoneNumber: apiUser.phoneNumber,
        address: apiUser.address,
        vppId: apiUser.vppId,
        province: apiUser.province,
        city: apiUser.city,
        district: apiUser.district,
        createTime: apiUser.create_time,
        updateTime: apiUser.update_time
      };
    },

    // 加载用户列表
    async loadUsers() {
      // 检查是否有vppId
      if (!this.currentVppId) {
        return;
      }

      const queryData = {
        index: this.pagination.page - 1, // API使用0基索引
        limit: this.pagination.size,
        vppId: this.currentVppId
      };

      // 添加搜索条件
      if (this.search.keyword) {
        queryData.userName = this.search.keyword;
      }
      if (this.search.area && this.search.area.length > 0) {
        const [province, city, district] = this.search.area;
        if (province) queryData.province = province;
        if (city) queryData.city = city;
        if (district) queryData.district = district;
      }
      const response = await getUserPage(queryData);

      if (response.code === 0 && response.data) {
        this.tableData = response.data.map(user =>
          this.transformUserData(user)
        );
        this.pagination.total = response?.total || 0;
      }
    },

    // 加载统计数据
    async loadStatistics() {
      // 现在统计数据通过 statData props 传入，不需要 API 调用
      // 如果有传入的统计数据，直接使用
      if (this.statData) {
        this.statCards = { ...this.statData };
      } else {
        // 兜底：如果没有传入统计数据，保持原有的 API 调用逻辑
        await this.loadStatisticsFromAPI();
      }
    },

    // 兜底方法：从 API 加载统计数据（保持向后兼容）
    async loadStatisticsFromAPI() {
      // 检查是否有vppId
      if (!this.currentVppId) {
        return;
      }

      // 使用getUserPage获取当前VPP的所有用户来计算统计
      // 设置一个较大的limit来获取所有用户，或者可以分批获取
      const response = await getUserPage({
        index: 0,
        limit: 1000, // 假设单个VPP不会超过1000个用户
        vppId: this.currentVppId
      });

      if (response.code === 0) {
        const users = response.data;
        this.statCards.user = response?.total || 0; // 使用API返回的total

        // 计算资源总数 - 注意使用API返回的原始字段名
        this.statCards.resource = users.reduce((total, user) => {
          return total + (user.resource_count || 0);
        }, 0);

        // TODO: 站点和设备数量需要从其他API获取
        // 这里暂时保持为0，后续可以添加相应的API调用
      }
    },
    onSelectionChange(selection) {
      this.selectedUsers = selection;
    },
    async onBatchDelete() {
      if (this.selectedUsers.length === 0) {
        this.$message.warning($T("请选择要删除的用户"));
        return;
      }

      this.$confirm(
        $T(`确定要删除选中的 ${this.selectedUsers.length} 个用户吗？`),
        $T("批量删除确认"),
        {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        }
      )
        .then(async () => {
          const userIds = this.selectedUsers.map(user => user.id);
          const deleteNode = {
            parentId: this.currentVppId, // 虚拟电厂ID
            ids: userIds // 用户ID数组
          };

          const results = await deleteUser(deleteNode);
          if (results.code === 0) {
            this.$message.success($T("批量删除成功"));
          }
          // 清空选中状态
          this.selectedUsers = [];
          this.$refs.userTable.clearSelection();

          // 重新加载数据
          this.loadUsers();
          this.loadStatistics();

          // 通知父组件刷新树形结构
          this.$emit("refresh-tree");
        })
        .catch(() => {
          this.$message.info($T("已取消删除"));
        });
    },
    onAddUser() {
      this.userDialog.visible = true;
      this.userDialog.mode = "add";
      this.userDialog.userData = {};
    },
    onUserDialogClose() {
      this.userDialog.visible = false;
    },
    async onUserDialogSave(userData) {
      // 检查是否有vppId
      if (!this.currentVppId) {
        this.$message.error($T("请先选择虚拟电厂"));
        return;
      }

      // 转换数据格式并添加VPP ID到用户数据
      const userDataWithVpp = {
        userName: userData.userName,
        vppId: this.currentVppId,
        phoneNumber: userData.phoneNumber,
        contactPerson: userData.contactPerson,
        address: userData.address,
        province: userData.province,
        city: userData.city,
        district: userData.district,
        resourceCount: userData.resourceCount || 0
      };

      const response = await createUser(userDataWithVpp);

      if (response.code === 0) {
        this.$message.success($T("用户创建成功"));

        // 1. 关闭弹窗
        this.userDialog.visible = false;

        // 2. 刷新表格数据
        this.loadUsers();
        // 3. 更新统计卡片
        this.loadStatistics();
        // 4. 通知父组件刷新树形结构
        this.$emit("refresh-tree");
      }
    },
    async onDetail(row) {
      // 查看用户详情
      this.userDetailDialog.userData = row;
      this.userDetailDialog.visibleTrigger = Date.now();
    },
    async onEdit(row) {
      this.userDialog.userData = row;
      this.userDialog.mode = "edit";
      this.userDialog.visible = true;
    },
    async onUserDialogUpdate(editedData) {
      // 获取原始用户数据，保留不可编辑的字段
      const originalData = this.userDialog.userData;
      // 处理编辑数据
      // 转换数据格式为API需要的格式
      const updateData = {
        userName: editedData.userName, // 用户名不可编辑，使用原始数据
        contactPerson: editedData.contact || editedData.contactPerson,
        phoneNumber: editedData.phone || editedData.phoneNumber,
        address: editedData.address,
        vppId: originalData.vppId || this.currentVppId, // 使用原始数据或当前VPP ID
        province: editedData.province,
        city: editedData.city,
        district: editedData.district
      };
      const response = await updateUser(editedData.id, updateData);

      if (response.code === 0) {
        this.$message.success($T("用户信息更新成功"));

        // 关闭弹窗
        this.userDialog.visible = false;

        // 刷新表格数据
        this.loadUsers();
        this.loadStatistics();
      }
    },
    async onDelete(row) {
      this.$confirm($T("确认删除该用户？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(async () => {
          // 构造删除参数
          const deleteNode = {
            parentId: this.currentVppId, // 虚拟电厂ID
            ids: [row.id] // 用户ID数组
          };

          const response = await deleteUser(deleteNode);

          if (response.code === 0) {
            this.$message.success($T("删除成功"));

            // 重新加载数据
            this.loadUsers();
            this.loadStatistics();

            // 通知父组件刷新树形结构
            this.$emit("refresh-tree");
          }
        })
        .catch(() => {
          this.$message.info($T("已取消删除"));
        });
    },
    onPageSizeChange(size) {
      this.pagination.size = size;
      this.pagination.page = 1; // 重置到第一页
      this.loadUsers();
    },
    onPageChange(page) {
      this.pagination.page = page;
      this.loadUsers();
    }
  },
  beforeDestroy() {
    // 清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  }
};
</script>

<style scoped lang="scss">
.user-management {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.summary-section {
  background: var(--BG1);
  border-radius: var(--Ra);
  padding: var(--J4);
  margin-bottom: var(--J3);
}

.summary-cards {
  display: flex;
  gap: var(--J1);
}

.summary-card {
  flex: 1;
  background: var(--BG);
  border-radius: var(--Ra);
  padding: var(--J3);
}

.summary-content {
  display: flex;
  flex-direction: column;
  gap: var(--J0);
}

.summary-title {
  font-size: var(--Ab);
  color: var(--T3);
  font-weight: 400;
  line-height: 22px;
}

.summary-value {
  font-size: var(--Aa);
  font-weight: 500;
  line-height: var(--J2);
  line-height: 22px;
}

.user-value {
  color: var(--ZS);
}

.resource-value {
  color: var(--Sta2);
}

.site-value {
  color: var(--SEQ12);
}

.device-value {
  color: var(--SEQ8);
}

.data-section {
  background: var(--BG1);
  border-radius: var(--Ra1);
  padding: var(--J4);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.filter-bar {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--J3);
  margin-bottom: var(--J2);
}

.vpp-search-input {
  width: 240px;
  height: 32px;
}

.vpp-search-input .el-input__inner {
  height: 32px;
  line-height: 32px;
  padding: 1px 1px 1px var(--J1);
  border: 1px solid var(--B1);
  border-radius: var(--Ra);
  background: var(--BG1);
  font-size: var(--Aa);
  color: var(--T1);
  box-shadow: none;
}

.vpp-search-input .el-input__inner::placeholder {
  color: var(--T4);
  font-size: var(--Aa);
}

.vpp-search-input .el-input__inner:focus {
  border-color: var(--ZS);
  box-shadow: none;
}

.vpp-search-input .el-input__prefix {
  left: var(--J1);
}

.vpp-search-input .el-input__prefix .el-input__icon {
  color: var(--T4);
  font-size: var(--J2);
  line-height: 32px;
}

.vpp-select-group {
  display: flex;
  gap: var(--J2);
  flex: 1;
  align-items: center;
  min-height: 32px;
}

.vpp-select-item {
  display: flex;
  align-items: center;
  gap: var(--J1);
  min-width: 120px;
  height: 32px;
}

.vpp-select-label {
  color: var(--ZS);
  font-size: var(--Ab);
  font-weight: 400;
  line-height: var(--J2);
  white-space: nowrap;
}

.vpp-select-item .el-select .el-input__inner {
  border: 1px solid var(--B1);
  border-radius: var(--Ra);
  background: var(--BG1);
  color: var(--T1);
  font-size: var(--Aa);
  padding: 0 var(--J1);
  height: 32px;
  line-height: 32px;
}

.vpp-select-item .el-select .el-input__inner:focus {
  border-color: var(--ZS);
  box-shadow: none;
}

.vpp-select-item .el-select .el-input__suffix {
  right: 0;
}

.vpp-select-item .el-select .el-input__suffix .el-input__icon {
  color: var(--T3);
  font-size: var(--Ab);
}

/* 级联选择器样式 - 已废弃，使用select-prefix替代 */
.vpp-cascader {
  display: none;
}

.vpp-cascader-label {
  display: none;
}

.vpp-select-item .el-cascader .el-input__inner {
  border: 1px solid var(--B1);
  border-radius: var(--Ra);
  background: var(--BG1);
  color: var(--T1);
  font-size: var(--Aa);
  padding: 0 var(--J1);
  padding-left: 50px;
  height: 32px;
  line-height: 32px;
}

.select-prefix {
  color: var(--ZS);
  font-size: var(--Aa);
  font-weight: 400;
  margin-right: 8px;
}

.area-select-container {
  display: flex;
  align-items: center;
  border: 1px solid var(--B1);
  border-radius: var(--Ra);
  background: var(--BG1);
  height: 32px;
  padding: 0 var(--J1);
}

.area-label {
  color: var(--ZS);
  font-size: var(--Aa);
  font-weight: 400;
  white-space: nowrap;
  margin-right: var(--J1);
  flex-shrink: 0;
}

.area-cascader .el-input__inner {
  border: none !important;
  background: transparent !important;
  color: var(--T1);
  font-size: var(--Aa);
  height: 30px;
  line-height: 30px;
  padding: 0;
}

.area-cascader .el-input__inner:focus {
  box-shadow: none !important;
  border: none !important;
}

.area-cascader .el-input__suffix {
  right: -8px;
}

.vpp-select-item .el-cascader .el-input__inner:focus {
  border-color: var(--ZS);
  box-shadow: none;
}

.vpp-select-item .el-cascader .el-input__suffix {
  right: 0;
}

.vpp-select-item .el-cascader .el-input__suffix .el-input__icon {
  color: var(--T3);
  font-size: var(--Ab);
}

.vpp-action-buttons {
  display: flex;
  align-items: center;
}

.vpp-btn-outline {
  height: 32px;
  padding: 0 var(--J2);
  border: 1px solid var(--B2);
  border-radius: var(--Ra);
  background: var(--BG1);
  color: var(--T1);
  font-size: var(--Aa);
  font-weight: 400;
  line-height: var(--J3);
}

.vpp-btn-primary {
  height: 32px;
  padding: 0 var(--J2);
  border: none;
  border-radius: var(--Ra);
  background: var(--ZS);
  color: var(--T5);
  font-size: var(--Aa);
  font-weight: 400;
  line-height: var(--J3);
}

.table-section {
  background: var(--BG);
  border-radius: var(--Ra);
  /* padding: var(--J3); */
}

.table-section .el-table {
  background: transparent;
  border: none;
}

.table-section .el-table th {
  background: var(--BG);
  border-bottom: 1px solid var(--B2);
  color: var(--T3);
  font-size: var(--Ab);
  font-weight: 400;
  line-height: var(--J2);
  padding: var(--J1) var(--J3);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-section .el-table th .cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-section .el-table td {
  border-bottom: 1px solid var(--B2);
  color: var(--T1);
  font-size: var(--Aa);
  font-weight: 400;
  line-height: var(--J3);
  padding: var(--J1) var(--J3);
}

.table-footer {
  display: flex;
  align-items: center;
  justify-content: right;
  gap: var(--J2);
  margin-top: var(--J2);
  font-size: var(--Aa);
  color: var(--T1);
}

/* 批量删除按钮样式 */
.vpp-btn-danger {
  background: var(--Sta3);
  border-color: var(--Sta3);
  color: var(--T5);
}

.vpp-btn-danger:hover {
  background: var(--Sta3);
  border-color: var(--Sta3);
  opacity: 0.8;
}

.vpp-btn-danger:disabled {
  background: var(--B2);
  border-color: var(--B2);
  color: var(--T3);
  cursor: not-allowed;
}

.vpp-btn-danger:disabled:hover {
  background: var(--B2);
  border-color: var(--B2);
  opacity: 1;
}

/* 操作链接样式 */
.action-link {
  font-size: 14px;
  cursor: pointer;
  margin-right: 16px;
  text-decoration: none;
  transition: opacity 0.2s;
}

.detail-link {
  color: var(--ZS);
}

.detail-link:hover {
  opacity: 0.8;
}

.edit-link {
  color: var(--ZS);
}

.edit-link:hover {
  opacity: 0.8;
}

.delete-link {
  color: var(--Sta3);
}

.delete-link:hover {
  opacity: 0.8;
}

@media (max-width: 900px) {
  .filter-bar {
    flex-direction: column;
    gap: var(--J1);
    align-items: stretch;
  }

  .vpp-search-input {
    width: 100%;
  }

  .vpp-select-group {
    flex-direction: column;
    gap: var(--J1);
  }

  .vpp-select-item {
    min-width: auto;
    width: 100%;
  }

  .vpp-action-buttons {
    gap: var(--J1);
    flex-wrap: wrap;
  }

  .summary-cards {
    flex-direction: column;
    gap: var(--J1);
  }

  .table-footer {
    flex-direction: column;
    gap: var(--J1);
    align-items: center;
  }
}

.flex1 {
  flex: 1;
  min-width: 0;
  min-height: 0;
}
//级联选择器
.cascader-box {
  position: relative;
  display: inline-block;
  .cascader-text {
    position: absolute;
    left: 15px;
    font-size: 14px;
    @include font_color(ZS);
    z-index: 1;
    user-select: none;
    top: 0;
    line-height: 32px;
    font-weight: normal;
  }
}
.cascader-box :deep(.el-cascader .el-input__inner) {
  padding-left: 60px;
  width: 250px;
  font-size: 14px;
  line-height: 32px;
}

.cascader-box :deep(.el-cascader .el-cascader__tags) {
  padding-left: 60px;
}
</style>
